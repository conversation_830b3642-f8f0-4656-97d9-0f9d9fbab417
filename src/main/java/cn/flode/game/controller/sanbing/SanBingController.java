package cn.flode.game.controller.sanbing;

import cn.flode.game.controller.sanbing.dto.*;
import cn.flode.game.controller.sanbing.vo.*;
import cn.flode.game.enums.sanbing.GroupType;
import cn.flode.game.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 前提信息：一个用户可以在一个区（areaCode）创建一个联盟，可以在一个区内创建两个账号，每个账号只可以加入区内的一个联盟 <br>
 * 1、只有联盟的创建者和账号的创建者才能修改、删除账号信息 <br>
 * 2. 联盟账号数量限制100人，账号申请加入联盟，并且联盟的管理员审核通过后，该账号才能算作是联盟的账号 <br>
 * 3. 账号名在联盟内唯一 <br>
 * 4. 账号状态：待审核、审核通过、拒绝 <br>
 * 5. 每个联盟的管理者可以在联盟内可以创建多个分组，每个分组可以包含多个账号 <br>
 * 6. 每个账号可以申请加入多种类型的分组，GUAN_DU1和GUAN_DU2算同一种类型，联盟管理者可以将申请的账号移动到具体类型的某个分组，则账号申请通过，删除申请记录 <br>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sanBing")
@SecurityRequirement(name = "bearerAuth")
class SanBingController {

  private final SanBingCoalitionService coalitionService;
  private final SanBingAccountService accountService;
  private final SanBingGroupService groupService;
  private final SanBingGroupAccountService groupAccountService;
  private final SanBingApplyJoinGroupService applyJoinGroupService;
  private final SanBingQueryService queryService;

  /** 创建联盟 */
  @Operation(summary = "创建联盟", description = "创建一个新的三冰联盟")
  @PostMapping("/coalition")
  public CoalitionKey coalition(
      @Parameter(description = "联盟创建信息", required = true) @Validated @RequestBody
          CoalitionCreateDTO create) {
    return coalitionService.create(create.getAreaCode(), create.getName());
  }

  /** 创建账号 */
  @Operation(summary = "创建账号", description = "用户在指定区域创建账号，每个用户最多创建2个账号")
  @PostMapping("/account")
  public AccountCreateResult createAccount(
      @Parameter(description = "账号创建信息", required = true) @Validated @RequestBody
          AccountCreateDTO createDTO) {
    return accountService.createAccount(createDTO);
  }

  /** 申请加入联盟 */
  @Operation(summary = "申请加入联盟", description = "账号申请加入联盟，只能申请加入同区的联盟")
  @PostMapping("/account/join")
  public OperationResult joinCoalition(
      @Parameter(description = "加入联盟申请信息", required = true) @Validated @RequestBody
          AccountJoinCoalitionDTO joinDTO) {
    accountService.applyJoinCoalition(joinDTO);
    return OperationResult.success("申请提交成功，等待联盟管理者审核");
  }

  /** 审核账号申请 */
  @Operation(summary = "审核账号申请", description = "联盟管理者审核账号的加入申请")
  @PostMapping("/account/approve")
  public OperationResult approveAccount(
      @Parameter(description = "审核信息", required = true) @Validated @RequestBody
          AccountApproveDTO approveDTO) {
    accountService.approveAccount(approveDTO.getAccountId(), approveDTO.getApproved());
    String message = approveDTO.getApproved() ? "审核通过" : "审核拒绝";
    return OperationResult.success(message);
  }

  /** 更新账号信息 */
  @Operation(summary = "更新账号信息", description = "更新联盟账号信息，只有账号创建者和联盟管理者可以操作")
  @PutMapping("/account")
  public OperationResult updateAccount(
      @Parameter(description = "账号更新信息", required = true) @Validated @RequestBody
          AccountUpdateDTO updateDTO) {
    accountService.updateAccount(updateDTO);
    return OperationResult.success("账号信息更新成功");
  }

  /** 移除联盟账号 */
  @Operation(summary = "移除联盟账号", description = "联盟管理者移除账号，同时清空该账号的所有分组信息")
  @DeleteMapping("/account/{accountId}")
  public OperationResult removeAccount(
      @Parameter(description = "账号ID", required = true) @PathVariable Long accountId) {
    accountService.removeAccount(accountId);
    return OperationResult.success("账号移除成功");
  }

  /** 创建联盟分组 */
  @Operation(summary = "创建联盟分组", description = "联盟管理者创建分组")
  @PostMapping("/group")
  public OperationResult createGroup(
      @Parameter(description = "分组创建信息", required = true) @Validated @RequestBody
          GroupCreateDTO createDTO) {
    Long groupId = groupService.createGroup(createDTO);
    return OperationResult.success("分组创建成功，ID: " + groupId);
  }

  /** 更新联盟分组 */
  @Operation(summary = "更新联盟分组", description = "联盟管理者更新分组信息")
  @PutMapping("/group")
  public OperationResult updateGroup(
      @Parameter(description = "分组更新信息", required = true) @Validated @RequestBody
          GroupUpdateDTO updateDTO) {
    groupService.updateGroup(updateDTO);
    return OperationResult.success("分组信息更新成功");
  }

  /** 删除联盟分组 */
  @Operation(summary = "删除联盟分组", description = "联盟管理者删除分组，清空分组内所有账号")
  @DeleteMapping("/group/{groupId}")
  public OperationResult deleteGroup(
      @Parameter(description = "分组ID", required = true) @PathVariable Long groupId) {
    groupService.deleteGroup(groupId);
    return OperationResult.success("分组删除成功");
  }

  /** 申请加入分组类型 */
  @Operation(summary = "申请加入分组类型", description = "账号申请加入分组类型，每种类型只能申请一次")
  @PostMapping("/account/apply-group")
  public OperationResult applyJoinGroup(
      @Parameter(description = "申请加入分组信息", required = true) @Validated @RequestBody
          AccountApplyGroupDTO applyDTO) {
    applyJoinGroupService.applyJoinGroupType(applyDTO);
    return OperationResult.success("申请提交成功，等待联盟管理者安排");
  }

  /** 添加账号到分组 */
  @Operation(summary = "添加账号到分组", description = "联盟管理者将账号添加到具体分组。如果是GUAN_DU类型分组，会自动处理从另一个GUAN_DU分组的移动")
  @PostMapping("/group/add-account")
  public OperationResult addAccountToGroup(
      @Parameter(description = "添加账号到分组信息", required = true) @Validated @RequestBody
          AccountAddToGroupDTO addDTO) {
    groupAccountService.addAccountToGroup(addDTO);
    return OperationResult.success("账号添加到分组成功");
  }

  /** 清空分组类型账号 */
  @Operation(summary = "清空分组类型账号", description = "联盟管理者清空某个类型分组的所有账号")
  @DeleteMapping("/group/clear/{coalitionId}/{groupType}")
  public OperationResult clearGroupTypeAccounts(
      @Parameter(description = "联盟ID", required = true) @PathVariable Long coalitionId,
      @Parameter(description = "分组类型", required = true) @PathVariable GroupType groupType) {
    groupService.clearGroupTypeAccounts(coalitionId, groupType);
    return OperationResult.success("分组类型账号清空成功");
  }

  /** 查询用户账号信息 */
  @Operation(summary = "查询用户账号信息", description = "查询当前用户创建的所有账号信息，按区域分组")
  @GetMapping("/account/my")
  public List<AreaAccount> getUserAccounts() {
    return accountService.getUserAccounts();
  }

  /** 查询联盟详细信息 */
  @Operation(summary = "查询联盟详细信息", description = "根据联盟ID查询联盟详细信息，包括分组和账号信息")
  @GetMapping("/coalition/{coalitionId}")
  public CoalitionDetailInfo getCoalitionDetail(
      @Parameter(description = "联盟ID", required = true) @PathVariable Long coalitionId) {
    return queryService.getCoalitionDetail(coalitionId);
  }

  /** 账号退出联盟 */
  @Operation(summary = "账号退出联盟", description = "账号退出联盟，同时退出该联盟下的所有分组")
  @PostMapping("/account/{accountId}/quit")
  public OperationResult quitCoalition(
      @Parameter(description = "账号ID", required = true) @PathVariable Long accountId) {
    accountService.quitCoalition(accountId);
    return OperationResult.success("退出联盟成功");
  }
}
