package cn.flode.game.service;

import cn.flode.game.controller.sanbing.vo.CoalitionKey;
import cn.flode.game.controller.sanbing.vo.MyCoalitionInfo;
import cn.flode.game.entity.SanBingAccount;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.mapper.SanBingAccountMapper;
import cn.flode.game.mapper.SanBingCoalitionMapper;
import cn.flode.game.util.RandomUtils;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingCoalitionService extends ServiceImpl<SanBingCoalitionMapper, SanBingCoalition> {

  private final SanBingAccountMapper accountMapper;

  public CoalitionKey create(Integer areaCode, String name) {
    Long currentUserId = SecurityUtils.currentUserId();

    // 检查用户在该区域是否已创建联盟
    long existCount = mapper.selectCountByQuery(
        QueryWrapper.create()
            .eq(SanBingCoalition::getAreaCode, areaCode)
            .eq(SanBingCoalition::getManagerId, currentUserId)
    );
    if (existCount > 0) {
      throw new RuntimeException("在该区已创建过联盟");
    }

    SanBingCoalition coalition = SanBingCoalition.builder()
        .areaCode(areaCode)
        .name(name)
        .managerId(currentUserId)
        .build();
    String code;
    long count;
    do {
      code = RandomUtils.randomString(6);
      count = mapper.selectCountByQuery(QueryWrapper.create().eq(SanBingCoalition::getCode, code));
    } while (count > 0);
    coalition.setCode(code);
    save(coalition);
    CoalitionKey coalitionKey = new CoalitionKey();
    coalitionKey.setCode(code);
    return coalitionKey;
  }

  /**
   * 根据联盟代码查找联盟
   *
   * @param code 联盟代码
   * @return 联盟对象，如果不存在返回null
   */
  public SanBingCoalition getByCode(String code) {
    return mapper.selectOneByQuery(QueryWrapper.create().eq(SanBingCoalition::getCode, code));
  }

  /**
   * 查询用户创建的所有联盟信息
   *
   * @return 用户创建的联盟信息列表
   */
  public List<MyCoalitionInfo> getUserCoalitions() {
    Long currentUserId = SecurityUtils.currentUserId();

    // 查询用户创建的所有联盟
    List<SanBingCoalition> coalitions = list(
        QueryWrapper.create().eq(SanBingCoalition::getManagerId, currentUserId)
    );

    // 转换为VO并统计成员数量
    return coalitions.stream()
        .map(this::convertToMyCoalitionInfo)
        .collect(Collectors.toList());
  }

  /**
   * 转换联盟实体为我的联盟信息VO
   */
  private MyCoalitionInfo convertToMyCoalitionInfo(SanBingCoalition coalition) {
    // 统计联盟成员数量（已审核通过的）
    long memberCount = accountMapper.selectCountByQuery(
        QueryWrapper.create()
            .eq(SanBingAccount::getCoalitionId, coalition.getId())
            .eq(SanBingAccount::getStatus, ApproveStatus.APPROVED)
    );

    // 统计待审核成员数量
    long pendingMemberCount = accountMapper.selectCountByQuery(
        QueryWrapper.create()
            .eq(SanBingAccount::getCoalitionId, coalition.getId())
            .eq(SanBingAccount::getStatus, ApproveStatus.TO_BE_APPROVED)
    );

    return MyCoalitionInfo.builder()
        .coalitionId(coalition.getId())
        .name(coalition.getName())
        .code(coalition.getCode())
        .areaCode(coalition.getAreaCode())
        .memberCount(memberCount)
        .pendingMemberCount(pendingMemberCount)
        .build();
  }

}
