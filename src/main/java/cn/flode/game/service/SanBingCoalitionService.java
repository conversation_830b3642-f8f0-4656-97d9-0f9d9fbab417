package cn.flode.game.service;

import cn.flode.game.controller.sanbing.dto.CoalitionUpdateDTO;
import cn.flode.game.controller.sanbing.vo.CoalitionKey;
import cn.flode.game.controller.sanbing.vo.MyCoalitionInfo;
import cn.flode.game.entity.SanBingAccount;
import cn.flode.game.entity.SanBingCoalition;
import cn.flode.game.enums.sanbing.ApproveStatus;
import cn.flode.game.mapper.SanBingAccountMapper;
import cn.flode.game.mapper.SanBingCoalitionMapper;
import cn.flode.game.util.RandomUtils;
import cn.flode.game.util.SecurityUtils;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务层实现。
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
@RequiredArgsConstructor
public class SanBingCoalitionService extends ServiceImpl<SanBingCoalitionMapper, SanBingCoalition> {

  private final SanBingAccountMapper accountMapper;

  public CoalitionKey create(Integer areaCode, String name) {
    Long currentUserId = SecurityUtils.currentUserId();

    // 检查用户在该区域是否已创建联盟
    long existCount = mapper.selectCountByQuery(
        QueryWrapper.create()
            .eq(SanBingCoalition::getAreaCode, areaCode)
            .eq(SanBingCoalition::getManagerId, currentUserId)
    );
    if (existCount > 0) {
      throw new RuntimeException("在该区已创建过联盟");
    }

    SanBingCoalition coalition = SanBingCoalition.builder()
        .areaCode(areaCode)
        .name(name)
        .managerId(currentUserId)
        .build();
    String code;
    long count;
    do {
      code = RandomUtils.randomString(6);
      count = mapper.selectCountByQuery(QueryWrapper.create().eq(SanBingCoalition::getCode, code));
    } while (count > 0);
    coalition.setCode(code);
    save(coalition);

    // 自动加入该区内用户创建的未加入联盟的账号
    autoJoinUserAccounts(coalition);

    CoalitionKey coalitionKey = new CoalitionKey();
    coalitionKey.setCode(code);
    return coalitionKey;
  }

  /**
   * 根据联盟代码查找联盟
   *
   * @param code 联盟代码
   * @return 联盟对象，如果不存在返回null
   */
  public SanBingCoalition getByCode(String code) {
    return mapper.selectOneByQuery(QueryWrapper.create().eq(SanBingCoalition::getCode, code));
  }

  /**
   * 查询用户创建的所有联盟信息
   *
   * @return 用户创建的联盟信息列表
   */
  public List<MyCoalitionInfo> getUserCoalitions() {
    Long currentUserId = SecurityUtils.currentUserId();

    // 查询用户创建的所有联盟
    List<SanBingCoalition> coalitions = list(
        QueryWrapper.create().eq(SanBingCoalition::getManagerId, currentUserId)
    );

    // 转换为VO并统计成员数量
    return coalitions.stream()
        .map(this::convertToMyCoalitionInfo)
        .collect(Collectors.toList());
  }

  /**
   * 更新联盟信息
   *
   * @param updateDTO 更新联盟信息DTO
   */
  public void updateCoalition(CoalitionUpdateDTO updateDTO) {
    Long currentUserId = SecurityUtils.currentUserId();

    // 检查联盟是否存在
    SanBingCoalition coalition = getById(updateDTO.getCoalitionId());
    if (coalition == null) {
      throw new RuntimeException("联盟不存在");
    }

    // 检查是否为联盟管理者
    if (!currentUserId.equals(coalition.getManagerId())) {
      throw new RuntimeException("只有联盟管理者可以修改联盟信息");
    }

    // 检查联盟代码是否已存在（如果修改了代码）
    if (!coalition.getCode().equals(updateDTO.getCode())) {
      SanBingCoalition existingCoalition = getByCode(updateDTO.getCode());
      if (existingCoalition != null) {
        throw new RuntimeException("联盟代码已存在");
      }
    }

    // 更新联盟信息
    coalition.setName(updateDTO.getName());
    coalition.setCode(updateDTO.getCode());
    updateById(coalition);
  }

  /**
   * 自动加入用户在该区域内未加入联盟的账号
   */
  private void autoJoinUserAccounts(SanBingCoalition coalition) {
    Long currentUserId = SecurityUtils.currentUserId();

    // 查询用户在该区域内未加入联盟的账号
    List<SanBingAccount> unJoinedAccounts = accountMapper.selectListByQuery(
        QueryWrapper.create()
            .eq(SanBingAccount::getCreator, currentUserId)
            .eq(SanBingAccount::getAreaCode, coalition.getAreaCode())
            .isNull(SanBingAccount::getCoalitionId)
    );

    // 为每个账号设置默认的联盟阶位（level = 1）并自动审核通过
    for (SanBingAccount account : unJoinedAccounts) {
      // 检查账号名在联盟内是否唯一
      long nameCount = accountMapper.selectCountByQuery(
          QueryWrapper.create()
              .eq(SanBingAccount::getCoalitionId, coalition.getId())
              .eq(SanBingAccount::getName, account.getName())
      );

      // 如果账号名不重复，则自动加入联盟
      if (nameCount == 0) {
        account.setCoalitionId(coalition.getId());
        account.setLevel(1); // 设置默认阶位为1
        account.setStatus(ApproveStatus.APPROVED); // 自动审核通过
        accountMapper.update(account);
      }
    }
  }

  /**
   * 转换联盟实体为我的联盟信息VO
   */
  private MyCoalitionInfo convertToMyCoalitionInfo(SanBingCoalition coalition) {
    // 统计联盟成员数量（已审核通过的）
    long memberCount = accountMapper.selectCountByQuery(
        QueryWrapper.create()
            .eq(SanBingAccount::getCoalitionId, coalition.getId())
            .eq(SanBingAccount::getStatus, ApproveStatus.APPROVED)
    );

    // 统计待审核成员数量
    long pendingMemberCount = accountMapper.selectCountByQuery(
        QueryWrapper.create()
            .eq(SanBingAccount::getCoalitionId, coalition.getId())
            .eq(SanBingAccount::getStatus, ApproveStatus.TO_BE_APPROVED)
    );

    return MyCoalitionInfo.builder()
        .coalitionId(coalition.getId())
        .name(coalition.getName())
        .code(coalition.getCode())
        .areaCode(coalition.getAreaCode())
        .memberCount(memberCount)
        .pendingMemberCount(pendingMemberCount)
        .build();
  }

}
